import { Handlers, PageProps } from "$fresh/server.ts";

interface Totals {
  sales_team: string;
  decimal_cases: number;
  revenue: number;
}

interface Detail {
  sales_team: string;
  decimal_cases: number;
  revenue: number;
}

interface SalesData {
  totals: Totals;
  details: Detail[];
}

export const handler: Handlers<SalesData> = {
  async GET(_req, ctx) {
    const res = await fetch("http://localhost:8000/api/sales-team");
    const data: SalesData = await res.json();
    return ctx.render(data);
  },
};

export default function HomePage({ data }: PageProps<SalesData>) {
  return (
    <main class="container">
      <h1>Sales Team - 2025</h1>
      <table>
        <thead>
          <tr>
            <th>Sales Team</th>
            <th>Decimal Cases</th>
            <th>Revenue</th>
          </tr>
        </thead>
        <tbody>
          {data.details.map((row) => (
            <tr>
              <td>{row.sales_team}</td>
              <td>{row.decimal_cases.toLocaleString()}</td>
              <td>${row.revenue.toLocaleString()}</td>
            </tr>
          ))}
          <tr style="font-weight:bold;">
            <td>{data.totals.sales_team}</td>
            <td>{data.totals.decimal_cases.toLocaleString()}</td>
            <td>${data.totals.revenue.toLocaleString()}</td>
          </tr>
        </tbody>
      </table>
    </main>
  );
}
