# Adams Distributing Dashboard Example – Fresh
A simple dashboard project built with the Fresh framework to display sales team data for 2025.

## Quick Start
```bash
deno run -A -r https://fresh.deno.dev .
deno task start
```

## Purpose
This project demonstrates a lightweight approach to building a web-based dashboard using Deno’s Fresh framework.
It connects to a secure backend service that retrieves data from ClickHouse hosted on Elestio, ensuring credentials are never exposed to the client.

## Tech Stack
- ClickHouse — Cloud-hosted columnar database for high-performance analytics queries (via Elestio)
- Elestio — Managed hosting provider for ClickHouse and other services
- Fresh — Full-stack web framework for Deno
- Pico.css — Minimal CSS framework for clean, responsive design

## Features (MVP)
- Displays sales team data for the year 2025 in a responsive table
- Simple, clean UI with Pico.css
- Backend securely queries ClickHouse — client never connects directly
- No database passwords or credentials are exposed in the frontend

## Project Structure
```bash
dashboard-example-fresh/
├── components/        # Reusable UI components
├── islands/           # Interactive client-side components
├── routes/            # Page routes
│   └── index.tsx      # Main dashboard page
├── static/            # Static assets (CSS, images, etc.)
├── deno.json          # Task runner and configuration
└── README.md          # Project documentation
```

## Security Notes
- All ClickHouse queries are performed server-side in Fresh routes or API endpoints.
- The client browser never connects to ClickHouse directly.
- All database credentials are stored securely on the server and are never exposed in the frontend code or network traffic.

## Status
**Private project** — not intended for public use or distribution at this stage.